from flask import Flask, jsonify, request
from flask_cors import CORS
import os
import requests
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configure CORS for PropBolt domains
cors_origins = [
    'https://propbolt.com',
    'https://go.propbolt.com',
    'https://api.propbolt.com',
    'https://data.propbolt.com',
    'https://api1.propbolt.com',
    'http://localhost:3000'
]
CORS(app, origins=cors_origins, supports_credentials=True)

# Configuration
REAL_ESTATE_API_KEY = os.getenv('REAL_ESTATE_API_KEY', 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914')
REAL_ESTATE_API_URL = os.getenv('REAL_ESTATE_API_URL', 'https://api.realestateapi.com')

def validate_propbolt_api_key(api_key):
    """
    Validate PropBolt API key (pb_live_xxx or pb_test_xxx format)
    For now, we'll use a simple validation - in production this would check the database
    """
    if not api_key:
        return False, "API key required"

    # Check format
    if not (api_key.startswith('pb_live_') or api_key.startswith('pb_test_')):
        # Also allow the admin fallback key for testing
        if api_key == REAL_ESTATE_API_KEY:
            return True, None
        return False, "Invalid API key format. Must start with 'pb_live_' or 'pb_test_'"

    # In production, this would check the database for:
    # - Key validity
    # - Usage limits
    # - Account status
    # For now, we'll accept any properly formatted key
    return True, None

def extract_api_key(request):
    """Extract API key from request headers"""
    # Check x-api-key header (primary)
    api_key = request.headers.get('x-api-key')
    if api_key:
        return api_key

    # Check Authorization header (Bearer token)
    auth_header = request.headers.get('Authorization', '')
    if auth_header.startswith('Bearer '):
        return auth_header[7:]  # Remove 'Bearer ' prefix

    # Check query parameter as fallback
    return request.args.get('api_key')

def transform_common_mistakes(endpoint_path, data):
    """Transform common parameter naming mistakes"""
    if not data:
        return data

    # Common transformations for all endpoints
    transformations = {
        'zipcode': 'zip_code',
        'zip': 'zip_code',
        'search': 'query',       # Common mistake: search -> query
        'term': 'query',         # Common mistake: term -> query
        'q': 'query',           # Common mistake: q -> query
    }

    # Endpoint-specific transformations
    if endpoint_path == '/v2/AutoComplete':
        transformations.update({'address': 'query'})  # AutoComplete uses 'query'
    elif endpoint_path == '/v2/PropertyDetailBulk':
        transformations.update({'addresses': 'ids', 'properties': 'ids'})  # Bulk needs 'ids'
    elif endpoint_path == '/v2/AddressVerification':
        transformations.update({'address': 'addresses'})  # Verification needs array
    elif endpoint_path == '/v2/CSVBuilder':
        # CSV Builder keeps 'addresses' as is - no transformation needed
        pass
    elif endpoint_path == '/v2/PropertyMapping':
        # Property Mapping keeps 'addresses' as is - no transformation needed
        pass
    else:
        # For most other endpoints, convert 'addresses' to 'address' (single property)
        transformations.update({'addresses': 'address'})

    # Apply transformations
    transformed = {}
    for key, value in data.items():
        new_key = transformations.get(key, key)
        transformed[new_key] = value

    return transformed

def proxy_to_real_estate_api(endpoint_path, user_data, user_api_key):
    """
    Proxy request to RealEstateAPI.com with proper authentication
    """
    try:
        # Transform common parameter mistakes
        user_data = transform_common_mistakes(endpoint_path, user_data)

        # Construct the full URL
        url = f"{REAL_ESTATE_API_URL}{endpoint_path}"

        # Headers for external API call
        headers = {
            'content-type': 'application/json',
            'x-api-key': REAL_ESTATE_API_KEY,  # Our admin key
            'x-user-id': user_api_key  # User's API key for tracking
        }

        # Make the request to RealEstateAPI.com
        response = requests.post(url, json=user_data, headers=headers, timeout=30)

        # Return the response
        return {
            'success': True,
            'data': response.json() if response.content else {},
            'status_code': response.status_code,
            'timestamp': datetime.utcnow().isoformat()
        }, response.status_code

    except requests.exceptions.Timeout:
        return {
            'error': 'Request timeout',
            'message': 'The external API request timed out',
            'timestamp': datetime.utcnow().isoformat()
        }, 504
    except requests.exceptions.RequestException as e:
        return {
            'error': 'External API error',
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }, 502
    except Exception as e:
        logger.error(f"Proxy error: {e}")
        return {
            'error': 'Internal server error',
            'message': 'An unexpected error occurred',
            'timestamp': datetime.utcnow().isoformat()
        }, 500

@app.route('/')
def root():
    """API information"""
    return jsonify({
        'message': 'PropBolt API1 Service',
        'version': '1.0.0',
        'description': 'Real Estate Data API Service for api1.propbolt.com',
        'endpoints': {
            'health': 'GET /health',
            'property_search': 'POST /v2/PropertySearch',
            'property_details': 'POST /v2/PropertyDetail',
            'property_detail_bulk': 'POST /v2/PropertyDetailBulk',
            'property_parcel': 'POST /v1/PropertyParcel',
            'property_comps_v2': 'POST /v2/PropertyComps',
            'property_comps_v3': 'POST /v3/PropertyComps',
            'autocomplete': 'POST /v2/AutoComplete',
            'address_verification': 'POST /v2/AddressVerification',
            'propgpt': 'POST /v2/PropGPT',
            'csv_builder': 'POST /v2/CSVBuilder',
            'property_avm': 'POST /v2/PropertyAvm',
            'property_liens': 'POST /v2/Reports/PropertyLiens',
            'property_mapping': 'POST /v2/PropertyMapping'
        },
        'authentication': 'x-api-key header with PropBolt API key (pb_live_xxx or pb_test_xxx)',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'service': 'api1.propbolt.com'
    })

@app.route('/test')
def test_interface():
    """Interactive testing interface"""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PropBolt API1 Service - Interactive Testing</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
            .header h1 { font-size: 2.5em; margin-bottom: 10px; }
            .header p { font-size: 1.2em; opacity: 0.9; }
            .endpoint-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
            .endpoint-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .endpoint-title { font-size: 1.3em; font-weight: bold; color: #333; margin-bottom: 10px; }
            .endpoint-method { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; margin-right: 10px; }
            .endpoint-path { font-family: monospace; background: #f8f9fa; padding: 4px 8px; border-radius: 4px; }
            .form-group { margin: 15px 0; }
            .form-group label { display: block; margin-bottom: 5px; font-weight: 500; }
            .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: monospace; }
            .form-group textarea { height: 100px; resize: vertical; }
            .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 1em; }
            .btn:hover { background: #0056b3; }
            .btn:disabled { background: #6c757d; cursor: not-allowed; }
            .response { margin-top: 15px; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9em; white-space: pre-wrap; }
            .response.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .response.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            .auth-section { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .auth-section h3 { color: #856404; margin-bottom: 10px; }
            .global-api-key { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: monospace; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 PropBolt API1 Service</h1>
                <p>Interactive Testing Interface - Real Estate Data API</p>
            </div>

            <div class="auth-section">
                <h3>🔑 Global API Key Configuration</h3>
                <p>Set your API key here to use across all endpoints:</p>
                <input type="text" id="globalApiKey" class="global-api-key" placeholder="Enter your API key (pb_live_xxx, pb_test_xxx, or admin key)" value="pb_test_demo123">
            </div>

            <div class="endpoint-grid">
                <!-- Property Search -->
                <div class="endpoint-card">
                    <div class="endpoint-title">Property Search</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropertySearch</span></div>
                    <p>Advanced property search and filtering</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertySearchBody">{"address": "123 Main St", "city": "Los Angeles", "state": "CA", "size": 10}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropertySearch', 'propertySearchBody', 'propertySearchResponse')">Test Endpoint</button>
                    <div id="propertySearchResponse" class="response" style="display:none;"></div>
                </div>

                <!-- Property Detail -->
                <div class="endpoint-card">
                    <div class="endpoint-title">Property Detail</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropertyDetail</span></div>
                    <p>Get comprehensive property information</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyDetailBody">{"address": "123 Main St, Los Angeles, CA"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropertyDetail', 'propertyDetailBody', 'propertyDetailResponse')">Test Endpoint</button>
                    <div id="propertyDetailResponse" class="response" style="display:none;"></div>
                </div>

                <!-- AutoComplete -->
                <div class="endpoint-card">
                    <div class="endpoint-title">AutoComplete</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/AutoComplete</span></div>
                    <p>Property search based on incomplete address parts</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="autoCompleteBody">{"search": "123 Main"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/AutoComplete', 'autoCompleteBody', 'autoCompleteResponse')">Test Endpoint</button>
                    <div id="autoCompleteResponse" class="response" style="display:none;"></div>
                </div>

                <!-- Property Comparables v2 -->
                <div class="endpoint-card">
                    <div class="endpoint-title">Property Comparables v2</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropertyComps</span></div>
                    <p>Generate property comparables for valuation</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyCompsBody">{"address": "123 Main St, Los Angeles, CA"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropertyComps', 'propertyCompsBody', 'propertyCompsResponse')">Test Endpoint</button>
                    <div id="propertyCompsResponse" class="response" style="display:none;"></div>
                </div>

                <!-- Property Detail Bulk -->
                <div class="endpoint-card">
                    <div class="endpoint-title">Property Detail Bulk</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropertyDetailBulk</span></div>
                    <p>Retrieve up to 1000 properties at once</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyDetailBulkBody">{"addresses": ["123 Main St, Los Angeles, CA", "456 Oak Ave, San Francisco, CA"]}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropertyDetailBulk', 'propertyDetailBulkBody', 'propertyDetailBulkResponse')">Test Endpoint</button>
                    <div id="propertyDetailBulkResponse" class="response" style="display:none;"></div>
                </div>

                <!-- Property Parcel -->
                <div class="endpoint-card">
                    <div class="endpoint-title">Property Parcel</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v1/PropertyParcel</span></div>
                    <p>Property boundaries in GeoJSON format</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyParcelBody">{"address": "123 Main St, Los Angeles, CA"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v1/PropertyParcel', 'propertyParcelBody', 'propertyParcelResponse')">Test Endpoint</button>
                    <div id="propertyParcelResponse" class="response" style="display:none;"></div>
                </div>

                <!-- More endpoints in a second row -->
                <div class="endpoint-card">
                    <div class="endpoint-title">Property Comparables v3</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v3/PropertyComps</span></div>
                    <p>Advanced comparables with customizable parameters</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyCompsV3Body">{"address": "123 Main St, Los Angeles, CA", "max_radius_miles": 1.0, "max_results": 10}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v3/PropertyComps', 'propertyCompsV3Body', 'propertyCompsV3Response')">Test Endpoint</button>
                    <div id="propertyCompsV3Response" class="response" style="display:none;"></div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-title">Address Verification</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/AddressVerification</span></div>
                    <p>Verify 1-100 addresses for accuracy</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="addressVerificationBody">{"addresses": [{"address": "123 Main St, Los Angeles, CA"}, {"address": "456 Oak Ave, San Francisco, CA"}]}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/AddressVerification', 'addressVerificationBody', 'addressVerificationResponse')">Test Endpoint</button>
                    <div id="addressVerificationResponse" class="response" style="display:none;"></div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-title">PropGPT</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropGPT</span></div>
                    <p>AI-powered natural language property search</p>
                    <p><strong>⚠️ Note:</strong> Requires OpenAI API key in x-openai-key header</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propGptBody">{"query": "Find me a 3 bedroom house in Los Angeles under $500k"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropGPT', 'propGptBody', 'propGptResponse')">Test Endpoint</button>
                    <div id="propGptResponse" class="response" style="display:none;"></div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-title">CSV Builder</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/CSVBuilder</span></div>
                    <p>Generate CSV exports of property data</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="csvBuilderBody">{"city": "Los Angeles", "state": "CA", "map": ["address", "price", "bedrooms"]}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/CSVBuilder', 'csvBuilderBody', 'csvBuilderResponse')">Test Endpoint</button>
                    <div id="csvBuilderResponse" class="response" style="display:none;"></div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-title">Property AVM</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropertyAvm</span></div>
                    <p>Lender Grade AVM for property valuations</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyAvmBody">{"address": "123 Main St, Los Angeles, CA 90210"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropertyAvm', 'propertyAvmBody', 'propertyAvmResponse')">Test Endpoint</button>
                    <div id="propertyAvmResponse" class="response" style="display:none;"></div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-title">Property Liens</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/Reports/PropertyLiens</span></div>
                    <p>Get property lien information</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyLiensBody">{"address": "123 Main St, Los Angeles, CA"}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/Reports/PropertyLiens', 'propertyLiensBody', 'propertyLiensResponse')">Test Endpoint</button>
                    <div id="propertyLiensResponse" class="response" style="display:none;"></div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-title">Property Mapping</div>
                    <div><span class="endpoint-method">POST</span><span class="endpoint-path">/v2/PropertyMapping</span></div>
                    <p>Create map pins for PropTech applications</p>
                    <div class="form-group">
                        <label>Request Body (JSON):</label>
                        <textarea id="propertyMappingBody">{"addresses": ["123 Main St, Los Angeles, CA", "456 Oak Ave, San Francisco, CA"]}</textarea>
                    </div>
                    <button class="btn" onclick="testEndpoint('/v2/PropertyMapping', 'propertyMappingBody', 'propertyMappingResponse')">Test Endpoint</button>
                    <div id="propertyMappingResponse" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <script>
            async function testEndpoint(endpoint, bodyId, responseId) {
                const apiKey = document.getElementById('globalApiKey').value;
                const body = document.getElementById(bodyId).value;
                const responseDiv = document.getElementById(responseId);

                if (!apiKey) {
                    responseDiv.innerHTML = 'Error: Please set an API key';
                    responseDiv.className = 'response error';
                    responseDiv.style.display = 'block';
                    return;
                }

                try {
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'x-api-key': apiKey
                        },
                        body: body
                    });

                    const data = await response.text();
                    responseDiv.innerHTML = `Status: ${response.status}\\n\\n${data}`;
                    responseDiv.className = response.ok ? 'response success' : 'response error';
                    responseDiv.style.display = 'block';
                } catch (error) {
                    responseDiv.innerHTML = `Error: ${error.message}`;
                    responseDiv.className = 'response error';
                    responseDiv.style.display = 'block';
                }
            }
        </script>
    </body>
    </html>
    """

# Authentication decorator
def require_api_key(f):
    """Decorator to require PropBolt API key authentication"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = extract_api_key(request)
        is_valid, error_msg = validate_propbolt_api_key(api_key)

        if not is_valid:
            return jsonify({
                'error': 'Authentication failed',
                'message': error_msg,
                'timestamp': datetime.utcnow().isoformat()
            }), 401

        # Add API key to request context for use in the endpoint
        request.user_api_key = api_key
        return f(*args, **kwargs)

    return decorated_function

# Real Estate API Endpoints
@app.route('/v2/PropertySearch', methods=['POST'])
@require_api_key
def property_search():
    """Property Search API - Advanced property search and filtering"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertySearch', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/PropertyDetail', methods=['POST'])
@require_api_key
def property_detail():
    """Property Detail API - Get comprehensive property information"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyDetail', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/AutoComplete', methods=['POST'])
@require_api_key
def autocomplete():
    """AutoComplete API - Property search based on incomplete address parts"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/AutoComplete', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/PropertyComps', methods=['POST'])
@require_api_key
def property_comps():
    """Property Comparables API v2 - Generate property comparables for valuation"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyComps', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/PropertyDetailBulk', methods=['POST'])
@require_api_key
def property_detail_bulk():
    """Property Detail Bulk API - Retrieve up to 1000 properties at once"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyDetailBulk', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v1/PropertyParcel', methods=['POST'])
@require_api_key
def property_parcel():
    """Property Boundary API - Returns parcel boundaries in GEOJSON format"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v1/PropertyParcel', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v3/PropertyComps', methods=['POST'])
@require_api_key
def property_comps_v3():
    """Property Comparables API v3 - Advanced comparables with customizable parameters"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v3/PropertyComps', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/AddressVerification', methods=['POST'])
@require_api_key
def address_verification():
    """Address Verification API - Verify 1-100 addresses for accuracy"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/AddressVerification', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/PropGPT', methods=['POST'])
@require_api_key
def propgpt():
    """PropGPT API - Natural language property search using AI"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropGPT', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/CSVBuilder', methods=['POST'])
@require_api_key
def csv_builder():
    """CSV Generator API - Generate CSV exports of property data"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/CSVBuilder', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/PropertyAvm', methods=['POST'])
@require_api_key
def property_avm():
    """Lender Grade AVM API - Get precise property valuations"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyAvm', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/Reports/PropertyLiens', methods=['POST'])
@require_api_key
def property_liens():
    """Involuntary Liens API - Get property lien information"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/Reports/PropertyLiens', data, request.user_api_key)
    return jsonify(result), status_code

@app.route('/v2/PropertyMapping', methods=['POST'])
@require_api_key
def property_mapping():
    """Mapping (Pins) API - Create map pins for PropTech applications"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyMapping', data, request.user_api_key)
    return jsonify(result), status_code

# API Documentation endpoints
@app.route('/docs')
def swagger_ui():
    """Swagger UI API documentation"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>PropBolt API1 Service - API Documentation</title>
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
        <style>
            html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
            *, *:before, *:after { box-sizing: inherit; }
            body { margin:0; background: #fafafa; }
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
        <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
        <script>
            window.onload = function() {
                const ui = SwaggerUIBundle({
                    url: '/openapi.json',
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIStandalonePreset
                    ],
                    plugins: [
                        SwaggerUIBundle.plugins.DownloadUrl
                    ],
                    layout: "StandaloneLayout"
                });
            };
        </script>
    </body>
    </html>
    """

@app.route('/openapi.json')
def openapi_spec():
    """Complete OpenAPI 3.0 specification for all 13 PropBolt API1 Service endpoints"""
    return jsonify({
        "openapi": "3.0.0",
        "info": {
            "title": "PropBolt API1 Service",
            "description": "Complete Real Estate Data API Service for api1.propbolt.com - All 13 Real Estate API endpoints with authentication",
            "version": "1.0.0",
            "contact": {
                "name": "PropBolt Support",
                "email": "<EMAIL>",
                "url": "https://propbolt.com"
            },
            "license": {
                "name": "PropBolt License",
                "url": "https://propbolt.com/license"
            }
        },
        "servers": [
            {
                "url": f"https://{request.host}",
                "description": "Production server"
            }
        ],
        "security": [
            {
                "ApiKeyAuth": []
            }
        ],
        "components": {
            "securitySchemes": {
                "ApiKeyAuth": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "x-api-key",
                    "description": "API key for authentication (format: pb_live_xxx or pb_test_xxx)"
                }
            },
            "schemas": {
                "Error": {
                    "type": "object",
                    "properties": {
                        "error": {"type": "string"},
                        "message": {"type": "string"},
                        "timestamp": {"type": "string", "format": "date-time"}
                    }
                },
                "SuccessResponse": {
                    "type": "object",
                    "properties": {
                        "success": {"type": "boolean"},
                        "data": {"type": "object"},
                        "timestamp": {"type": "string", "format": "date-time"}
                    }
                }
            }
        },
        "paths": {
            "/": {
                "get": {
                    "summary": "API Information",
                    "description": "Get API information and available endpoints",
                    "security": [],
                    "responses": {
                        "200": {
                            "description": "API information",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "message": {"type": "string"},
                                            "version": {"type": "string"},
                                            "endpoints": {"type": "object"},
                                            "timestamp": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/health": {
                "get": {
                    "summary": "Health Check",
                    "description": "Health check endpoint for monitoring service status",
                    "security": [],
                    "responses": {
                        "200": {
                            "description": "Service health status",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "status": {"type": "string"},
                                            "timestamp": {"type": "string"},
                                            "version": {"type": "string"},
                                            "service": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/v2/PropertySearch": {
                "post": {
                    "summary": "Property Search API",
                    "description": "Searchable API for list building, search counts, and advanced filtering on properties",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "city": {"type": "string", "description": "City name"},
                                        "state": {"type": "string", "description": "State abbreviation"},
                                        "zip": {"type": "string", "description": "ZIP code"},
                                        "beds": {"type": "object", "description": "Bedroom filter"},
                                        "baths": {"type": "object", "description": "Bathroom filter"},
                                        "value": {"type": "object", "description": "Property value filter"},
                                        "size": {"type": "integer", "default": 50, "description": "Number of results to return"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St",
                                    "city": "Los Angeles",
                                    "state": "CA",
                                    "size": 10
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property search results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/PropertyDetail": {
                "post": {
                    "summary": "Property Detail API",
                    "description": "Get comprehensive property information including ownership, sales history, and valuations",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "id": {"type": "string", "description": "Property ID"},
                                        "apn": {"type": "string", "description": "Assessor Parcel Number"},
                                        "city": {"type": "string", "description": "City name"},
                                        "state": {"type": "string", "description": "State abbreviation"},
                                        "zip": {"type": "string", "description": "ZIP code"},
                                        "exact_match": {"type": "boolean", "default": False, "description": "Require exact address match"},
                                        "comps": {"type": "boolean", "default": False, "description": "Include comparable properties"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St, Los Angeles, CA"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/PropertyDetailBulk": {
                "post": {
                    "summary": "Property Detail Bulk API",
                    "description": "Retrieve up to 1000 properties at once",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "ids": {"type": "array", "items": {"type": "string"}, "description": "Array of property IDs"},
                                        "addresses": {"type": "array", "items": {"type": "string"}, "description": "Array of property addresses"}
                                    }
                                },
                                "example": {
                                    "addresses": ["123 Main St, Los Angeles, CA", "456 Oak Ave, San Francisco, CA"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Bulk property details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v1/PropertyParcel": {
                "post": {
                    "summary": "Property Boundary API",
                    "description": "Returns parcel boundaries in GEOJSON format",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "id": {"type": "string", "description": "Property ID"},
                                        "apn": {"type": "string", "description": "Assessor Parcel Number"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St, Los Angeles, CA"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property parcel boundaries", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/PropertyComps": {
                "post": {
                    "summary": "Property Comparables API v2",
                    "description": "Generate property comparables for valuation",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "id": {"type": "string", "description": "Property ID"},
                                        "id": {"type": "string", "description": "Property ID from search results"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St, Los Angeles, CA"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property comparables", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v3/PropertyComps": {
                "post": {
                    "summary": "Property Comparables API v3",
                    "description": "Advanced comparables with customizable parameters",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "id": {"type": "string", "description": "Property ID"},
                                        "max_radius_miles": {"type": "number", "default": 1.0, "description": "Maximum search radius in miles"},
                                        "max_days_back": {"type": "integer", "default": 180, "description": "Maximum days back for sales data"},
                                        "max_results": {"type": "integer", "default": 10, "description": "Maximum number of comparables"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St, Los Angeles, CA",
                                    "max_radius_miles": 1.0,
                                    "max_results": 10
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Advanced property comparables", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/AutoComplete": {
                "post": {
                    "summary": "AutoComplete API",
                    "description": "Property search based on incomplete address parts",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "search": {"type": "string", "description": "Partial address to search"},
                                        "latitude": {"type": "number", "description": "Latitude for location-based search"},
                                        "longitude": {"type": "number", "description": "Longitude for location-based search"}
                                    }
                                },
                                "example": {
                                    "search": "123 Main"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Address autocomplete suggestions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/AddressVerification": {
                "post": {
                    "summary": "Address Verification API",
                    "description": "Verify 1-100 addresses for accuracy",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "addresses": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "address": {"type": "string", "description": "Complete formatted address"},
                                                    "city": {"type": "string", "description": "City name"},
                                                    "state": {"type": "string", "description": "State abbreviation"},
                                                    "zip": {"type": "string", "description": "ZIP code"},
                                                    "key": {"type": "string", "description": "User-provided key for matching"}
                                                }
                                            },
                                            "description": "Array of address objects to verify"
                                        },
                                        "strict": {"type": "boolean", "default": False, "description": "Use strict verification mode"}
                                    }
                                },
                                "example": {
                                    "addresses": [
                                        {"address": "123 Main St, Los Angeles, CA"},
                                        {"address": "456 Oak Ave, San Francisco, CA"}
                                    ]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Address verification results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/PropGPT": {
                "post": {
                    "summary": "PropGPT API",
                    "description": "Natural language property search using AI",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {"type": "string", "description": "Natural language query"},
                                        "size": {"type": "integer", "default": 50, "description": "Number of results"},
                                        "model": {"type": "string", "default": "gpt-4o", "description": "AI model to use"}
                                    }
                                },
                                "example": {
                                    "query": "Find me a 3 bedroom house in Los Angeles under $500k"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "AI-powered property search results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/CSVBuilder": {
                "post": {
                    "summary": "CSV Generator API",
                    "description": "Generate CSV exports of property data",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "addresses": {"type": "array", "items": {"type": "string"}, "description": "Array of property addresses"},
                                        "fields": {"type": "array", "items": {"type": "string"}, "description": "Fields to include in CSV"},
                                        "format": {"type": "string", "default": "csv", "description": "Output format"}
                                    }
                                },
                                "example": {
                                    "addresses": ["123 Main St, Los Angeles, CA"],
                                    "fields": ["address", "price", "bedrooms", "bathrooms"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "CSV export data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/PropertyAvm": {
                "post": {
                    "summary": "Lender Grade AVM API",
                    "description": "Get precise property valuations",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "id": {"type": "string", "description": "Property ID"},
                                        "strict": {"type": "boolean", "default": False, "description": "Use strict valuation mode"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St, Los Angeles, CA"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property valuation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/Reports/PropertyLiens": {
                "post": {
                    "summary": "Involuntary Liens API",
                    "description": "Get property lien information",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "id": {"type": "string", "description": "Property ID"},
                                        "zip": {"type": "string", "description": "ZIP code"},
                                        "apn": {"type": "string", "description": "Assessor Parcel Number"}
                                    }
                                },
                                "example": {
                                    "address": "123 Main St, Los Angeles, CA"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property lien information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            },
            "/v2/PropertyMapping": {
                "post": {
                    "summary": "Mapping (Pins) API",
                    "description": "Create map pins for PropTech applications",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string", "description": "Property address"},
                                        "city": {"type": "string", "description": "City name"},
                                        "state": {"type": "string", "description": "State abbreviation"},
                                        "zip": {"type": "string", "description": "ZIP code"},
                                        "size": {"type": "integer", "default": 50, "description": "Number of pins to return"}
                                    }
                                },
                                "example": {
                                    "addresses": ["123 Main St, Los Angeles, CA", "456 Oak Ave, San Francisco, CA"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property mapping pins", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}},
                        "401": {"description": "Unauthorized - Invalid API key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}},
                        "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}
                    }
                }
            }
        }
    })

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Endpoint not found',
        'message': 'The requested endpoint does not exist',
        'available_endpoints': [
            '/health', '/v2/PropertySearch', '/v2/PropertyDetail', '/v2/PropertyDetailBulk',
            '/v1/PropertyParcel', '/v2/PropertyComps', '/v3/PropertyComps', '/v2/AutoComplete',
            '/v2/AddressVerification', '/v2/PropGPT', '/v2/CSVBuilder', '/v2/PropertyAvm',
            '/v2/Reports/PropertyLiens', '/v2/PropertyMapping'
        ],
        'timestamp': datetime.utcnow().isoformat()
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred',
        'timestamp': datetime.utcnow().isoformat()
    }), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    logger.info(f"Starting PropBolt API1 Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=False)
